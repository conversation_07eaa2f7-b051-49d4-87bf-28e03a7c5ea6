# ESPECIFICAC<PERSON>ÓN COMPLETA - MICROSERVICIO STORE
# ===============================================

## 1. CONFIGURACIÓN GENERAL

### Tecnología
- **Framework**: NestJS + TypeScript
- **Base de Datos**: MongoDB (DbWeb)
- **Puerto**: 3001
- **Base URL**: http://localhost:3001/api/store
- **Autenticación**: JWT Bearer Token

### Conexión MongoDB
```env
DB_HOST=localhost
DB_PORT=27017
DB_NAME=DbWeb
```

### Headers Requeridos
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

---

## 2. COMPONENTES FRONTEND QUE UTILIZAN EL MICROSERVICIO

### DASHBOARD
- `src/app/menu/store/dashboard/page.tsx` - Página principal
- `src/components/store/DashboardSummary.tsx` - Estadísticas generales
- `src/components/store/RecentOrders.tsx` - Pedidos recientes

### PRODUCTOS
- `src/app/menu/store/products/page.tsx` - Gestión de productos
- `src/components/store/ProductsList.tsx` - Lista de productos
- `src/components/store/ProductForm.tsx` - Formulario crear/editar

### PEDIDOS
- `src/app/menu/store/orders/page.tsx` - Gestión de pedidos
- `src/components/store/OrdersList.tsx` - Lista de pedidos
- `src/components/store/OrderDetail.tsx` - Detalle de pedido

### PROVEEDORES
- `src/app/menu/store/suppliers/page.tsx` - Gestión de proveedores
- `src/components/store/SuppliersList.tsx` - Lista de proveedores
- `src/components/store/SupplierForm.tsx` - Formulario proveedores

### RESEÑAS
- `src/app/menu/store/reviews/page.tsx` - Gestión de reseñas
- `src/components/store/ReviewsList.tsx` - Lista de reseñas

### SERVICIOS FRONTEND
- `src/services/api/store.ts` - Cliente API para store
- `src/hooks/useStore.ts` - Hooks React para store

---

## 3. ENDPOINTS DEL MICROSERVICIO

### 3.1 DASHBOARD - Estadísticas
```
GET /dashboard
```
**Respuesta**:
```json
{
  "totalProducts": 25,
  "pendingOrders": 8,
  "completedOrders": 142,
  "totalRevenue": 15420.50,
  "averageRating": 4.3
}
```

### 3.2 PRODUCTOS - CRUD Completo
```
GET /products              - Obtener todos los productos
POST /products             - Crear producto
GET /products/:id          - Obtener producto específico
PATCH /products/:id        - Actualizar producto
DELETE /products/:id       - Eliminar producto
```

**Modelo Producto**:
```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "price": "number",
  "stock": "number",
  "category": "string",
  "image": "string",
  "tags": ["string"],
  "deliveryOptions": {
    "delivery": "boolean",
    "pickup": "boolean"
  },
  "nutritionalInfo": {
    "calories": "number",
    "protein": "number",
    "carbs": "number",
    "fat": "number"
  },
  "isActive": "boolean",
  "storeId": "string",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.3 PEDIDOS - Gestión de Orders
```
GET /orders                - Obtener todos los pedidos
GET /orders/:id            - Obtener pedido específico
PATCH /orders/:id/status   - Actualizar estado del pedido
```

**Modelo Pedido**:
```json
{
  "id": "string",
  "customer": {
    "name": "string",
    "address": "string",
    "phone": "string",
    "email": "string"
  },
  "date": "string",
  "total": "number",
  "status": "pending|processing|ready|delivered|cancelled",
  "deliveryMethod": "delivery|pickup",
  "items": [{
    "id": "string",
    "name": "string",
    "quantity": "number",
    "price": "number"
  }],
  "notes": "string",
  "storeId": "string",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.4 PROVEEDORES - CRUD Completo
```
GET /suppliers             - Obtener todos los proveedores
POST /suppliers            - Crear proveedor
GET /suppliers/:id         - Obtener proveedor específico
PATCH /suppliers/:id       - Actualizar proveedor
DELETE /suppliers/:id      - Eliminar proveedor
```

**Modelo Proveedor**:
```json
{
  "id": "string",
  "name": "string",
  "contact": {
    "email": "string",
    "phone": "string",
    "address": "string"
  },
  "products": ["string"],
  "rating": "number",
  "isActive": "boolean",
  "storeId": "string",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.5 RESEÑAS - Gestión de Reviews
```
GET /reviews               - Obtener todas las reseñas
GET /reviews/:id           - Obtener reseña específica
PATCH /reviews/:id/reply   - Responder a reseña
```

**Modelo Reseña**:
```json
{
  "id": "string",
  "customer": {
    "name": "string",
    "email": "string"
  },
  "rating": "number",
  "comment": "string",
  "date": "string",
  "reply": {
    "message": "string",
    "date": "string"
  },
  "orderId": "string",
  "storeId": "string",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

---

## 4. ESTRUCTURA NESTJS REQUERIDA

### 4.1 Módulos
```
src/
├── app.module.ts
├── main.ts
├── store/
│   ├── store.module.ts
│   ├── controllers/
│   │   ├── dashboard.controller.ts
│   │   ├── products.controller.ts
│   │   ├── orders.controller.ts
│   │   ├── suppliers.controller.ts
│   │   └── reviews.controller.ts
│   ├── services/
│   │   ├── dashboard.service.ts
│   │   ├── products.service.ts
│   │   ├── orders.service.ts
│   │   ├── suppliers.service.ts
│   │   └── reviews.service.ts
│   ├── schemas/
│   │   ├── product.schema.ts
│   │   ├── order.schema.ts
│   │   ├── supplier.schema.ts
│   │   └── review.schema.ts
│   └── dto/
│       ├── create-product.dto.ts
│       ├── update-product.dto.ts
│       ├── create-supplier.dto.ts
│       └── update-supplier.dto.ts
└── auth/
    ├── auth.module.ts
    ├── guards/
    │   ├── jwt-auth.guard.ts
    │   └── store.guard.ts
    └── strategies/
        └── jwt.strategy.ts
```

### 4.2 Comandos NestJS CLI
```bash
# Generar estructura base
nest new store-microservice
cd store-microservice

# Instalar dependencias
npm install @nestjs/mongoose mongoose
npm install @nestjs/jwt @nestjs/passport passport passport-jwt
npm install @nestjs/config class-validator class-transformer

# Generar módulos
nest generate module store
nest generate module auth

# Generar controladores
nest generate controller store/dashboard
nest generate controller store/products
nest generate controller store/orders
nest generate controller store/suppliers
nest generate controller store/reviews

# Generar servicios
nest generate service store/dashboard
nest generate service store/products
nest generate service store/orders
nest generate service store/suppliers
nest generate service store/reviews
```

---

## 5. INTEGRACIÓN FRONTEND

### 5.1 Cliente API (src/services/api/store.ts)
```typescript
import axios from 'axios';

class StoreAPI {
  private baseURL = 'http://localhost:3001/api/store';
  private token = localStorage.getItem('token');

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json'
    };
  }

  // Dashboard
  async getDashboardStats() {
    const response = await axios.get(`${this.baseURL}/dashboard`, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  // Productos
  async getProducts() {
    const response = await axios.get(`${this.baseURL}/products`, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async createProduct(productData: any) {
    const response = await axios.post(`${this.baseURL}/products`, productData, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async updateProduct(id: string, productData: any) {
    const response = await axios.patch(`${this.baseURL}/products/${id}`, productData, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async deleteProduct(id: string) {
    const response = await axios.delete(`${this.baseURL}/products/${id}`, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  // Pedidos
  async getOrders() {
    const response = await axios.get(`${this.baseURL}/orders`, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async getOrder(id: string) {
    const response = await axios.get(`${this.baseURL}/orders/${id}`, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async updateOrderStatus(id: string, status: string) {
    const response = await axios.patch(`${this.baseURL}/orders/${id}/status`,
      { status },
      { headers: this.getHeaders() }
    );
    return response.data;
  }

  // Proveedores
  async getSuppliers() {
    const response = await axios.get(`${this.baseURL}/suppliers`, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async createSupplier(supplierData: any) {
    const response = await axios.post(`${this.baseURL}/suppliers`, supplierData, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async updateSupplier(id: string, supplierData: any) {
    const response = await axios.patch(`${this.baseURL}/suppliers/${id}`, supplierData, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async deleteSupplier(id: string) {
    const response = await axios.delete(`${this.baseURL}/suppliers/${id}`, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  // Reseñas
  async getReviews() {
    const response = await axios.get(`${this.baseURL}/reviews`, {
      headers: this.getHeaders()
    });
    return response.data;
  }

  async replyToReview(id: string, reply: string) {
    const response = await axios.patch(`${this.baseURL}/reviews/${id}/reply`,
      { reply },
      { headers: this.getHeaders() }
    );
    return response.data;
  }
}

export const storeAPI = new StoreAPI();
```

### 5.2 Hooks React (src/hooks/useStore.ts)
```typescript
import { useState, useEffect } from 'react';
import { storeAPI } from '@/services/api/store';

// Hook para Dashboard
export const useDashboard = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const data = await storeAPI.getDashboardStats();
      setStats(data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return { stats, loading, error, refetch: fetchStats };
};

// Hook para Productos
export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await storeAPI.getProducts();
      setProducts(data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createProduct = async (productData) => {
    try {
      const newProduct = await storeAPI.createProduct(productData);
      setProducts(prev => [newProduct, ...prev]);
      return newProduct;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const updateProduct = async (id, productData) => {
    try {
      const updatedProduct = await storeAPI.updateProduct(id, productData);
      setProducts(prev => prev.map(p => p.id === id ? updatedProduct : p));
      return updatedProduct;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const deleteProduct = async (id) => {
    try {
      await storeAPI.deleteProduct(id);
      setProducts(prev => prev.filter(p => p.id !== id));
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  return {
    products,
    loading,
    error,
    createProduct,
    updateProduct,
    deleteProduct,
    refetch: fetchProducts
  };
};

// Hook para Pedidos
export const useOrders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const data = await storeAPI.getOrders();
      setOrders(data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (id, status) => {
    try {
      const updatedOrder = await storeAPI.updateOrderStatus(id, status);
      setOrders(prev => prev.map(o => o.id === id ? updatedOrder : o));
      return updatedOrder;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  return {
    orders,
    loading,
    error,
    updateOrderStatus,
    refetch: fetchOrders
  };
};

// Hook para Proveedores
export const useSuppliers = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      const data = await storeAPI.getSuppliers();
      setSuppliers(data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createSupplier = async (supplierData) => {
    try {
      const newSupplier = await storeAPI.createSupplier(supplierData);
      setSuppliers(prev => [newSupplier, ...prev]);
      return newSupplier;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const updateSupplier = async (id, supplierData) => {
    try {
      const updatedSupplier = await storeAPI.updateSupplier(id, supplierData);
      setSuppliers(prev => prev.map(s => s.id === id ? updatedSupplier : s));
      return updatedSupplier;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  const deleteSupplier = async (id) => {
    try {
      await storeAPI.deleteSupplier(id);
      setSuppliers(prev => prev.filter(s => s.id !== id));
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  return {
    suppliers,
    loading,
    error,
    createSupplier,
    updateSupplier,
    deleteSupplier,
    refetch: fetchSuppliers
  };
};

// Hook para Reseñas
export const useReviews = () => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const data = await storeAPI.getReviews();
      setReviews(data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const replyToReview = async (id, reply) => {
    try {
      const updatedReview = await storeAPI.replyToReview(id, reply);
      setReviews(prev => prev.map(r => r.id === id ? updatedReview : r));
      return updatedReview;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  useEffect(() => {
    fetchReviews();
  }, []);

  return {
    reviews,
    loading,
    error,
    replyToReview,
    refetch: fetchReviews
  };
};
```

---

## 6. ESQUEMAS MONGODB (MONGOOSE)

### 6.1 Product Schema
```typescript
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Product extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true, min: 0 })
  price: number;

  @Prop({ required: true, min: 0 })
  stock: number;

  @Prop({ required: true })
  category: string;

  @Prop()
  image: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({
    type: {
      delivery: { type: Boolean, default: true },
      pickup: { type: Boolean, default: true }
    },
    default: { delivery: true, pickup: true }
  })
  deliveryOptions: {
    delivery: boolean;
    pickup: boolean;
  };

  @Prop({
    type: {
      calories: { type: Number, default: 0 },
      protein: { type: Number, default: 0 },
      carbs: { type: Number, default: 0 },
      fat: { type: Number, default: 0 }
    }
  })
  nutritionalInfo: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ required: true })
  storeId: string;
}

export const ProductSchema = SchemaFactory.createForClass(Product);
```

### 6.2 Order Schema
```typescript
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Order extends Document {
  @Prop({
    type: {
      name: { type: String, required: true },
      address: String,
      phone: { type: String, required: true },
      email: String
    },
    required: true
  })
  customer: {
    name: string;
    address?: string;
    phone: string;
    email?: string;
  };

  @Prop({ required: true })
  date: string;

  @Prop({ required: true, min: 0 })
  total: number;

  @Prop({
    required: true,
    enum: ['pending', 'processing', 'ready', 'delivered', 'cancelled'],
    default: 'pending'
  })
  status: string;

  @Prop({
    required: true,
    enum: ['delivery', 'pickup']
  })
  deliveryMethod: string;

  @Prop({
    type: [{
      id: { type: String, required: true },
      name: { type: String, required: true },
      quantity: { type: Number, required: true, min: 1 },
      price: { type: Number, required: true, min: 0 }
    }],
    required: true
  })
  items: {
    id: string;
    name: string;
    quantity: number;
    price: number;
  }[];

  @Prop()
  notes: string;

  @Prop({ required: true })
  storeId: string;
}

export const OrderSchema = SchemaFactory.createForClass(Order);
```

### 6.3 Supplier Schema
```typescript
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Supplier extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({
    type: {
      email: { type: String, required: true },
      phone: { type: String, required: true },
      address: { type: String, required: true }
    },
    required: true
  })
  contact: {
    email: string;
    phone: string;
    address: string;
  };

  @Prop({ type: [String], default: [] })
  products: string[];

  @Prop({ min: 0, max: 5, default: 0 })
  rating: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ required: true })
  storeId: string;
}

export const SupplierSchema = SchemaFactory.createForClass(Supplier);
```

### 6.4 Review Schema
```typescript
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Review extends Document {
  @Prop({
    type: {
      name: { type: String, required: true },
      email: { type: String, required: true }
    },
    required: true
  })
  customer: {
    name: string;
    email: string;
  };

  @Prop({ required: true, min: 1, max: 5 })
  rating: number;

  @Prop({ required: true })
  comment: string;

  @Prop({ required: true })
  date: string;

  @Prop({
    type: {
      message: String,
      date: String
    }
  })
  reply: {
    message: string;
    date: string;
  };

  @Prop()
  orderId: string;

  @Prop({ required: true })
  storeId: string;
}

export const ReviewSchema = SchemaFactory.createForClass(Review);
```

---

## 7. CONTROLADORES NESTJS

### 7.1 Dashboard Controller
```typescript
import { Controller, Get, UseGuards, Req } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { StoreGuard } from '../../auth/guards/store.guard';
import { DashboardService } from '../services/dashboard.service';

@Controller('store/dashboard')
@UseGuards(JwtAuthGuard, StoreGuard)
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get()
  async getStats(@Req() req: any) {
    const storeId = req.user.storeId;
    return await this.dashboardService.getStats(storeId);
  }
}
```

### 7.2 Products Controller
```typescript
import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  UseGuards,
  Req
} from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { StoreGuard } from '../../auth/guards/store.guard';
import { ProductsService } from '../services/products.service';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';

@Controller('store/products')
@UseGuards(JwtAuthGuard, StoreGuard)
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Get()
  async findAll(@Req() req: any) {
    const storeId = req.user.storeId;
    return await this.productsService.findAll(storeId);
  }

  @Post()
  async create(@Body() createProductDto: CreateProductDto, @Req() req: any) {
    const storeId = req.user.storeId;
    return await this.productsService.create(createProductDto, storeId);
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Req() req: any) {
    const storeId = req.user.storeId;
    return await this.productsService.findOne(id, storeId);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @Req() req: any
  ) {
    const storeId = req.user.storeId;
    return await this.productsService.update(id, updateProductDto, storeId);
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Req() req: any) {
    const storeId = req.user.storeId;
    return await this.productsService.remove(id, storeId);
  }
}
```

---

## 8. SERVICIOS NESTJS

### 8.1 Dashboard Service
```typescript
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product } from '../schemas/product.schema';
import { Order } from '../schemas/order.schema';
import { Review } from '../schemas/review.schema';

@Injectable()
export class DashboardService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<Product>,
    @InjectModel(Order.name) private orderModel: Model<Order>,
    @InjectModel(Review.name) private reviewModel: Model<Review>,
  ) {}

  async getStats(storeId: string) {
    const [
      totalProducts,
      pendingOrders,
      completedOrders,
      revenueData,
      avgRating
    ] = await Promise.all([
      this.productModel.countDocuments({ storeId, isActive: true }),
      this.orderModel.countDocuments({ storeId, status: 'pending' }),
      this.orderModel.countDocuments({ storeId, status: 'delivered' }),
      this.orderModel.aggregate([
        { $match: { storeId, status: 'delivered' } },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),
      this.reviewModel.aggregate([
        { $match: { storeId } },
        { $group: { _id: null, avg: { $avg: '$rating' } } }
      ])
    ]);

    return {
      totalProducts,
      pendingOrders,
      completedOrders,
      totalRevenue: revenueData[0]?.total || 0,
      averageRating: avgRating[0]?.avg || 0
    };
  }
}
```

---

## 9. VALIDACIÓN Y DTOs

### 9.1 Create Product DTO
```typescript
import { IsString, IsNumber, IsBoolean, IsArray, IsOptional, Min } from 'class-validator';

export class CreateProductDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsNumber()
  @Min(0)
  price: number;

  @IsNumber()
  @Min(0)
  stock: number;

  @IsString()
  category: string;

  @IsOptional()
  @IsString()
  image?: string;

  @IsOptional()
  @IsArray()
  tags?: string[];

  @IsOptional()
  deliveryOptions?: {
    delivery: boolean;
    pickup: boolean;
  };

  @IsOptional()
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}
```

---

## 10. CONFIGURACIÓN MAIN.TS
```typescript
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Configurar CORS
  app.enableCors({
    origin: 'http://localhost:3000', // Frontend URL
    credentials: true,
  });

  // Configurar validación global
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Configurar prefijo global
  app.setGlobalPrefix('api');

  await app.listen(3001);
  console.log('Store Microservice running on http://localhost:3001');
}
bootstrap();
```

---

## 11. DATOS MOCK EN ESPAÑOL (CULTURA CHILENA)

### Productos de Ejemplo
```json
[
  {
    "name": "Empanadas de Pino",
    "description": "Empanadas tradicionales chilenas rellenas de carne, cebolla y aceitunas",
    "price": 2500,
    "stock": 50,
    "category": "Comida Tradicional",
    "tags": ["tradicional", "chileno", "empanadas"]
  },
  {
    "name": "Completo Italiano",
    "description": "Hot dog con palta, tomate y mayonesa",
    "price": 3200,
    "stock": 30,
    "category": "Comida Rápida",
    "tags": ["completo", "italiano", "rápido"]
  },
  {
    "name": "Sopaipillas con Pebre",
    "description": "Sopaipillas caseras acompañadas de pebre tradicional",
    "price": 1800,
    "stock": 40,
    "category": "Aperitivos",
    "tags": ["sopaipillas", "pebre", "casero"]
  }
]
```

### Proveedores de Ejemplo
```json
[
  {
    "name": "Distribuidora Los Andes",
    "contact": {
      "email": "<EMAIL>",
      "phone": "+56 2 2345 6789",
      "address": "Av. Providencia 1234, Santiago"
    },
    "products": ["Carnes", "Verduras"],
    "rating": 4.5
  },
  {
    "name": "Panadería San Miguel",
    "contact": {
      "email": "<EMAIL>",
      "phone": "+56 2 8765 4321",
      "address": "Calle San Miguel 567, Santiago"
    },
    "products": ["Pan", "Masas"],
    "rating": 4.8
  }
]
```

---

## 12. COMANDOS DE INSTALACIÓN Y EJECUCIÓN

```bash
# 1. Crear proyecto NestJS
npm i -g @nestjs/cli
nest new store-microservice
cd store-microservice

# 2. Instalar dependencias
npm install @nestjs/mongoose mongoose
npm install @nestjs/jwt @nestjs/passport passport passport-jwt
npm install @nestjs/config class-validator class-transformer
npm install bcryptjs

# 3. Configurar variables de entorno (.env)
DB_HOST=localhost
DB_PORT=27017
DB_NAME=DbWeb
JWT_SECRET=tu_jwt_secret_aqui
PORT=3001

# 4. Ejecutar en desarrollo
npm run start:dev

# 5. Ejecutar en producción
npm run build
npm run start:prod
```

---

## 13. TESTING Y VALIDACIÓN

### Endpoints de Prueba con cURL
```bash
# Obtener estadísticas del dashboard
curl -X GET http://localhost:3001/api/store/dashboard \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Crear un producto
curl -X POST http://localhost:3001/api/store/products \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Empanadas de Pino",
    "description": "Empanadas tradicionales chilenas",
    "price": 2500,
    "stock": 50,
    "category": "Comida Tradicional"
  }'

# Obtener todos los productos
curl -X GET http://localhost:3001/api/store/products \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Actualizar estado de pedido
curl -X PATCH http://localhost:3001/api/store/orders/ORDER_ID/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status": "processing"}'
```

---

## RESUMEN DE IMPLEMENTACIÓN

1. **Crear microservicio NestJS** con estructura modular
2. **Configurar MongoDB** con esquemas Mongoose
3. **Implementar autenticación JWT** con guards de seguridad
4. **Desarrollar controladores** para cada funcionalidad
5. **Crear servicios** con lógica de negocio
6. **Configurar validación** con DTOs y class-validator
7. **Integrar con frontend** usando cliente API y hooks React
8. **Poblar con datos mock** en español/chileno
9. **Probar endpoints** con herramientas como Postman o cURL
10. **Documentar API** para el equipo de desarrollo

Este microservicio proporcionará toda la funcionalidad necesaria para que los componentes frontend de store funcionen correctamente con datos reales desde MongoDB.
