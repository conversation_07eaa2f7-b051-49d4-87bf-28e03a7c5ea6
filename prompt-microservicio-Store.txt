# Prompt para Microservicio de Store - Especificaciones CRUD

## Descripción General
Este documento especifica los endpoints y estructuras de datos que debe implementar el microservicio de Store para funcionar correctamente con el frontend de la aplicación de delivery.

**TECNOLOGÍA**: NestJS + TypeScript + MongoDB + Mongoose
**BASE DE DATOS**: MongoDB (DbWeb)
**PUERTO**: 3001
**FRAMEWORK**: NestJS con decoradores y módulos
**LENGUAJE**: TypeScript

## Base URL
```
http://localhost:3001/api/store
```

## Autenticación
Todos los endpoints requieren autenticación Bearer Token en el header:
```
Authorization: Bearer <token>
```

---

## 1. DASHBOARD - Estadísticas de la Tienda

### Endpoint: GET /dashboard
**Descripción**: Obtiene estadísticas generales de la tienda

******* COMPONENTES FRONTEND QUE LO USAN *******
- `src/app/menu/store/dashboard/page.tsx` - Página principal del dashboard
- `src/components/store/DashboardSummary.tsx` - Componente de resumen de estadísticas
- `src/hooks/useStore.ts` - Hook `useDashboard()`

**Respuesta esperada**:
```json
{
  "totalProducts": 25,
  "pendingOrders": 8,
  "completedOrders": 142,
  "totalRevenue": 15420.50,
  "averageRating": 4.3
}
```

**Campos requeridos**:
- `totalProducts` (number): Total de productos activos
- `pendingOrders` (number): Pedidos pendientes de procesar
- `completedOrders` (number): Pedidos completados
- `totalRevenue` (number): Ingresos totales
- `averageRating` (number): Calificación promedio (0-5)

---

## 2. PRODUCTOS - Gestión de Catálogo

******* COMPONENTES FRONTEND QUE LO USAN *******
- `src/app/menu/store/products/page.tsx` - Página de gestión de productos
- `src/components/store/ProductsList.tsx` - Lista de productos
- `src/components/store/ProductForm.tsx` - Formulario de productos
- `src/hooks/useStore.ts` - Hook `useProducts()`

### GET /products
**Descripción**: Obtiene todos los productos de la tienda

**Respuesta esperada**:
```json
[
  {
    "_id": "64a1b2c3d4e5f6789012345",
    "name": "Empanadas de Pino",
    "description": "Deliciosas empanadas chilenas rellenas de carne, cebolla y aceitunas",
    "price": 2500,
    "stock": 50,
    "category": "Comida Tradicional",
    "image": "https://example.com/empanadas.jpg",
    "tags": ["tradicional", "carne", "chilena"],
    "deliveryOptions": {
      "delivery": true,
      "pickup": true
    },
    "nutritionalInfo": {
      "calories": 320,
      "protein": 15,
      "carbs": 25,
      "fat": 18
    }
  }
]
```

### POST /products
**Descripción**: Crea un nuevo producto

**Body esperado**:
```json
{
  "name": "Nombre del producto",
  "description": "Descripción detallada",
  "price": 2500,
  "stock": 50,
  "category": "Categoría",
  "image": "URL de la imagen",
  "tags": ["tag1", "tag2"],
  "deliveryOptions": {
    "delivery": true,
    "pickup": true
  },
  "nutritionalInfo": {
    "calories": 320,
    "protein": 15,
    "carbs": 25,
    "fat": 18
  }
}
```

### PATCH /products/:id
**Descripción**: Actualiza un producto existente

**Body**: Campos a actualizar (parcial del objeto producto)

### DELETE /products/:id
**Descripción**: Elimina un producto

---

## 3. PEDIDOS - Gestión de Orders

******* COMPONENTES FRONTEND QUE LO USAN *******
- `src/app/menu/store/orders/page.tsx` - Página de gestión de pedidos
- `src/app/menu/store/dashboard/page.tsx` - Dashboard (pedidos recientes)
- `src/components/store/OrdersList.tsx` - Lista de pedidos
- `src/components/store/OrderDetail.tsx` - Detalle de pedido
- `src/components/store/RecentOrders.tsx` - Pedidos recientes en dashboard
- `src/hooks/useStore.ts` - Hook `useOrders()`

### GET /orders
**Descripción**: Obtiene todos los pedidos
**Query params opcionales**: `?status=pending` para filtrar por estado

**Respuesta esperada**:
```json
[
  {
    "_id": "64a1b2c3d4e5f6789012346",
    "customer": {
      "name": "María González",
      "address": "Av. Providencia 1234, Santiago",
      "phone": "+56912345678",
      "email": "<EMAIL>"
    },
    "date": "2024-01-15T10:30:00Z",
    "total": 7500,
    "status": "pending",
    "deliveryMethod": "delivery",
    "items": [
      {
        "id": "64a1b2c3d4e5f6789012345",
        "name": "Empanadas de Pino",
        "quantity": 3,
        "price": 2500
      }
    ],
    "notes": "Sin aceitunas por favor"
  }
]
```

### GET /orders/:id
**Descripción**: Obtiene un pedido específico

### PATCH /orders/:id/status
**Descripción**: Actualiza el estado de un pedido

**Body esperado**:
```json
{
  "status": "processing"
}
```

**Estados válidos**: "pending", "processing", "ready", "delivered", "cancelled"

---

## 4. PROVEEDORES - Gestión de Suppliers

******* COMPONENTES FRONTEND QUE LO USAN *******
- `src/app/menu/store/suppliers/page.tsx` - Página de gestión de proveedores
- `src/components/store/SuppliersList.tsx` - Lista de proveedores
- `src/components/store/SupplierForm.tsx` - Formulario de proveedores
- `src/hooks/useStore.ts` - Hook `useSuppliers()`

### GET /suppliers
**Descripción**: Obtiene todos los proveedores

**Respuesta esperada**:
```json
[
  {
    "_id": "64a1b2c3d4e5f6789012347",
    "name": "Distribuidora San Miguel",
    "contactPerson": "Carlos Rodríguez",
    "email": "<EMAIL>",
    "phone": "+56987654321",
    "address": "Calle Los Aromos 567, Maipú",
    "products": ["Carnes", "Verduras", "Lácteos"]
  }
]
```

### POST /suppliers
**Descripción**: Crea un nuevo proveedor

**Body esperado**:
```json
{
  "name": "Nombre del proveedor",
  "contactPerson": "Persona de contacto",
  "email": "<EMAIL>",
  "phone": "+56912345678",
  "address": "Dirección completa",
  "products": ["Producto1", "Producto2"]
}
```

### PATCH /suppliers/:id
**Descripción**: Actualiza un proveedor

### DELETE /suppliers/:id
**Descripción**: Elimina un proveedor

---

## 5. RESEÑAS - Gestión de Reviews

******* COMPONENTES FRONTEND QUE LO USAN *******
- `src/app/menu/store/reviews/page.tsx` - Página de reseñas
- `src/components/store/ReviewsList.tsx` - Lista de reseñas con estadísticas
- `src/hooks/useStore.ts` - Hook `useReviews()`

### GET /reviews
**Descripción**: Obtiene todas las reseñas de la tienda

**Respuesta esperada**:
```json
{
  "reviews": [
    {
      "_id": "64a1b2c3d4e5f6789012348",
      "customer": "Ana Pérez",
      "rating": 5,
      "comment": "Excelente comida, muy sabrosa y llegó caliente",
      "date": "2024-01-14T15:45:00Z",
      "productId": "64a1b2c3d4e5f6789012345",
      "productName": "Empanadas de Pino"
    }
  ],
  "averageRating": 4.3,
  "totalReviews": 87
}
```

---

## 6. MANEJO DE ERRORES

### Códigos de Estado HTTP
- `200`: Éxito
- `201`: Creado exitosamente
- `400`: Error en los datos enviados
- `401`: No autorizado
- `404`: Recurso no encontrado
- `500`: Error interno del servidor

### Formato de Error
```json
{
  "error": true,
  "message": "Descripción del error",
  "code": "ERROR_CODE"
}
```

---

## 7. DATOS DE EJEMPLO CHILENOS

### Productos Sugeridos:
- Empanadas de Pino
- Pastel de Choclo
- Cazuela de Cordero
- Completos Italianos
- Sopaipillas
- Mote con Huesillo
- Churrasco Palta
- Humitas

### Categorías:
- Comida Tradicional
- Comida Rápida
- Bebidas
- Postres
- Vegetariano

### Nombres Chilenos:
- María González, Carlos Rodríguez, Ana Pérez, Luis Morales, Carmen Silva

### Direcciones Chilenas:
- Av. Providencia 1234, Santiago
- Calle Los Aromos 567, Maipú
- Pasaje Las Flores 89, Ñuñoa

---

## 8. NOTAS IMPORTANTES

1. **Todos los precios están en pesos chilenos (CLP)**
2. **Las fechas deben estar en formato ISO 8601**
3. **Los IDs deben ser ObjectId de MongoDB**
4. **Implementar validación de datos en todos los endpoints**
5. **Manejar casos donde no hay datos (devolver arrays vacíos, no errores)**
6. **Implementar paginación para listas grandes**
7. **Logs detallados para debugging**

---

## 9. ENDPOINTS ADICIONALES OPCIONALES

### GET /analytics/sales
**Descripción**: Datos para gráficos de ventas

### GET /inventory/low-stock
**Descripción**: Productos con stock bajo

### POST /products/bulk-update
**Descripción**: Actualización masiva de productos

---

## 10. EJEMPLOS DE IMPLEMENTACIÓN

### Estructura de Base de Datos (MongoDB)

******* BASE DE DATOS: MongoDB *******
******* COLECCIONES REQUERIDAS *******

#### Colección: products
```javascript
{
  _id: ObjectId,
  name: String,
  description: String,
  price: Number,
  stock: Number,
  category: String,
  image: String,
  tags: [String],
  deliveryOptions: {
    delivery: Boolean,
    pickup: Boolean
  },
  nutritionalInfo: {
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number
  },
  storeId: ObjectId, // ID de la tienda
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Colección: orders
```javascript
{
  _id: ObjectId,
  storeId: ObjectId,
  customer: {
    name: String,
    address: String,
    phone: String,
    email: String
  },
  items: [{
    productId: ObjectId,
    name: String,
    quantity: Number,
    price: Number
  }],
  total: Number,
  status: String, // pending, processing, ready, delivered, cancelled
  deliveryMethod: String, // delivery, pickup
  notes: String,
  createdAt: Date,
  updatedAt: Date
}
```

#### Colección: suppliers
```javascript
{
  _id: ObjectId,
  storeId: ObjectId,
  name: String,
  contactPerson: String,
  email: String,
  phone: String,
  address: String,
  products: [String],
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Colección: reviews
```javascript
{
  _id: ObjectId,
  storeId: ObjectId,
  productId: ObjectId,
  customer: String,
  rating: Number, // 1-5
  comment: String,
  productName: String,
  createdAt: Date,
  updatedAt: Date
}
```

#### Colección: stores (para referencia)
```javascript
{
  _id: ObjectId,
  userId: ObjectId, // Referencia al usuario
  name: String,
  address: String,
  phone: String,
  email: String,
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

### Ejemplo de Controller (NestJS + TypeScript)

```typescript
// src/store/controllers/dashboard.controller.ts
import { Controller, Get, UseGuards, Req } from '@nestjs/common';
import { DashboardService } from '../services/dashboard.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { StoreGuard } from '../../auth/guards/store.guard';

@Controller('store/dashboard')
@UseGuards(JwtAuthGuard, StoreGuard)
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get()
  async getDashboardStats(@Req() req: any) {
    const storeId = req.user.storeId;
    return await this.dashboardService.getStats(storeId);
  }
}
```

```typescript
// src/store/services/dashboard.service.ts
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product } from '../schemas/product.schema';
import { Order } from '../schemas/order.schema';
import { Review } from '../schemas/review.schema';

@Injectable()
export class DashboardService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<Product>,
    @InjectModel(Order.name) private orderModel: Model<Order>,
    @InjectModel(Review.name) private reviewModel: Model<Review>,
  ) {}

  async getStats(storeId: string) {
    const [
      totalProducts,
      pendingOrders,
      completedOrders,
      revenueData,
      avgRating
    ] = await Promise.all([
      this.productModel.countDocuments({ storeId, isActive: true }),
      this.orderModel.countDocuments({ storeId, status: 'pending' }),
      this.orderModel.countDocuments({ storeId, status: 'delivered' }),
      this.orderModel.aggregate([
        { $match: { storeId, status: 'delivered' } },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),
      this.reviewModel.aggregate([
        { $match: { storeId } },
        { $group: { _id: null, avg: { $avg: '$rating' } } }
      ])
    ]);

    return {
      totalProducts,
      pendingOrders,
      completedOrders,
      totalRevenue: revenueData[0]?.total || 0,
      averageRating: avgRating[0]?.avg || 0
    };
  }
}
```

### Guards de Autenticación (NestJS)

```typescript
// src/auth/guards/jwt-auth.guard.ts
import { Injectable, ExecutionContext } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }
}
```

```typescript
// src/auth/guards/store.guard.ts
import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';

@Injectable()
export class StoreGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || user.accountType !== 'Store') {
      throw new ForbiddenException('Acceso denegado: Solo usuarios de tipo Store');
    }

    return true;
  }
}
```

```typescript
// src/auth/strategies/jwt.strategy.ts
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET,
    });
  }

  async validate(payload: any) {
    if (!payload) {
      throw new UnauthorizedException();
    }
    return payload;
  }
}
```

---

## 11. VALIDACIONES REQUERIDAS

### DTOs de Validación (NestJS + class-validator)

```typescript
// src/store/dto/create-product.dto.ts
import { IsString, IsNumber, IsArray, IsBoolean, IsOptional, Min, MaxLength, MinLength, IsUrl } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateProductDto {
  @IsString()
  @MinLength(2)
  @MaxLength(100)
  name: string;

  @IsString()
  @MinLength(10)
  @MaxLength(500)
  description: string;

  @IsNumber()
  @Min(0)
  price: number;

  @IsNumber()
  @Min(0)
  stock: number;

  @IsString()
  @MinLength(2)
  @MaxLength(50)
  category: string;

  @IsUrl()
  image: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @IsOptional()
  deliveryOptions?: {
    delivery: boolean;
    pickup: boolean;
  };

  @IsOptional()
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };
}
```

```typescript
// src/store/dto/update-order-status.dto.ts
import { IsEnum } from 'class-validator';

export enum OrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  READY = 'ready',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

export class UpdateOrderStatusDto {
  @IsEnum(OrderStatus)
  status: OrderStatus;
}
```

---

## 12. CASOS ESPECIALES A MANEJAR

### 1. Tienda Nueva (Sin Datos)
- Dashboard debe devolver todos los valores en 0
- Listas deben devolver arrays vacíos
- No mostrar errores, sino estados vacíos

### 2. Productos Agotados
- Mantener en la lista pero marcar stock: 0
- Permitir edición para reponer stock

### 3. Pedidos Cancelados
- Mantener historial pero marcar como cancelled
- No incluir en cálculos de revenue

### 4. Manejo de Imágenes
- Validar URLs de imágenes
- Tener imagen por defecto si falla la carga
- Considerar subida de archivos local

---

## 13. TESTING RECOMENDADO

### Datos de Prueba
```javascript
// Crear productos de prueba
const testProducts = [
  {
    name: "Empanadas de Pino",
    description: "Tradicionales empanadas chilenas con carne, cebolla y aceitunas",
    price: 2500,
    stock: 30,
    category: "Comida Tradicional",
    image: "https://example.com/empanadas.jpg",
    tags: ["tradicional", "carne", "chilena"]
  },
  // ... más productos
];
```

### Casos de Prueba Importantes
1. CRUD completo para cada entidad
2. Filtros y búsquedas
3. Validaciones de datos
4. Manejo de errores
5. Autenticación y autorización
6. Performance con datos grandes

---

## 14. CONFIGURACIÓN DE MONGODB

### Configuración de MongoDB (NestJS)

```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { StoreModule } from './store/store.module';
import { AuthModule } from './auth/auth.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: `mongodb://${configService.get('DB_HOST')}:${configService.get('DB_PORT')}/${configService.get('DB_NAME')}`,
      }),
      inject: [ConfigService],
    }),
    StoreModule,
    AuthModule,
  ],
})
export class AppModule {}
```

### Variables de Entorno (.env)
```
# Configuración de la base de datos
DB_HOST=localhost
DB_PORT=27017
DB_NAME=DbWeb

# JWT
JWT_SECRET=tu_jwt_secret_aqui

# Servidor
PORT=3001
NODE_ENV=development
```

### Índices Recomendados para MongoDB
```javascript
// Crear índices para optimizar consultas
db.products.createIndex({ storeId: 1, isActive: 1 });
db.orders.createIndex({ storeId: 1, status: 1 });
db.orders.createIndex({ storeId: 1, createdAt: -1 });
db.suppliers.createIndex({ storeId: 1, isActive: 1 });
db.reviews.createIndex({ storeId: 1 });
db.reviews.createIndex({ productId: 1 });
```

### Schemas de Mongoose (NestJS + TypeScript)

```typescript
// src/store/schemas/product.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ProductDocument = Product & Document;

@Schema({ timestamps: true })
export class Product {
  @Prop({ required: true, trim: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true, min: 0 })
  price: number;

  @Prop({ required: true, min: 0 })
  stock: number;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true })
  image: string;

  @Prop([String])
  tags: string[];

  @Prop({
    type: {
      delivery: { type: Boolean, default: true },
      pickup: { type: Boolean, default: true }
    },
    default: { delivery: true, pickup: true }
  })
  deliveryOptions: {
    delivery: boolean;
    pickup: boolean;
  };

  @Prop({
    type: {
      calories: Number,
      protein: Number,
      carbs: Number,
      fat: Number
    }
  })
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };

  @Prop({ type: Types.ObjectId, ref: 'Store', required: true })
  storeId: Types.ObjectId;

  @Prop({ default: true })
  isActive: boolean;
}

export const ProductSchema = SchemaFactory.createForClass(Product);
```

```typescript
// src/store/schemas/order.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type OrderDocument = Order & Document;

@Schema({ timestamps: true })
export class Order {
  @Prop({ type: Types.ObjectId, ref: 'Store', required: true })
  storeId: Types.ObjectId;

  @Prop({
    type: {
      name: { type: String, required: true },
      address: String,
      phone: { type: String, required: true },
      email: String
    },
    required: true
  })
  customer: {
    name: string;
    address?: string;
    phone: string;
    email?: string;
  };

  @Prop([{
    productId: { type: Types.ObjectId, ref: 'Product' },
    name: String,
    quantity: Number,
    price: Number
  }])
  items: Array<{
    productId: Types.ObjectId;
    name: string;
    quantity: number;
    price: number;
  }>;

  @Prop({ required: true })
  total: number;

  @Prop({
    enum: ['pending', 'processing', 'ready', 'delivered', 'cancelled'],
    default: 'pending'
  })
  status: string;

  @Prop({
    enum: ['delivery', 'pickup'],
    required: true
  })
  deliveryMethod: string;

  @Prop()
  notes?: string;
}

export const OrderSchema = SchemaFactory.createForClass(Order);
```

******* RESUMEN DE COMPONENTES FRONTEND *******

**DASHBOARD**:
- `src/app/menu/store/dashboard/page.tsx`
- `src/components/store/DashboardSummary.tsx`
- `src/components/store/RecentOrders.tsx`

**PRODUCTOS**:
- `src/app/menu/store/products/page.tsx`
- `src/components/store/ProductsList.tsx`
- `src/components/store/ProductForm.tsx`

**PEDIDOS**:
- `src/app/menu/store/orders/page.tsx`
- `src/components/store/OrdersList.tsx`
- `src/components/store/OrderDetail.tsx`

**PROVEEDORES**:
- `src/app/menu/store/suppliers/page.tsx`
- `src/components/store/SuppliersList.tsx`
- `src/components/store/SupplierForm.tsx`

**RESEÑAS**:
- `src/app/menu/store/reviews/page.tsx`
- `src/components/store/ReviewsList.tsx`

**HOOKS**:
- `src/hooks/useStore.ts` - Todos los hooks de store

**API SERVICES**:
- `src/services/api/store.ts` - Funciones de API

---

## 15. ESTRUCTURA DE MÓDULOS NESTJS

### Módulo Principal de Store
```typescript
// src/store/store.module.ts
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Controllers
import { DashboardController } from './controllers/dashboard.controller';
import { ProductsController } from './controllers/products.controller';
import { OrdersController } from './controllers/orders.controller';
import { SuppliersController } from './controllers/suppliers.controller';
import { ReviewsController } from './controllers/reviews.controller';

// Services
import { DashboardService } from './services/dashboard.service';
import { ProductsService } from './services/products.service';
import { OrdersService } from './services/orders.service';
import { SuppliersService } from './services/suppliers.service';
import { ReviewsService } from './services/reviews.service';

// Schemas
import { Product, ProductSchema } from './schemas/product.schema';
import { Order, OrderSchema } from './schemas/order.schema';
import { Supplier, SupplierSchema } from './schemas/supplier.schema';
import { Review, ReviewSchema } from './schemas/review.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Product.name, schema: ProductSchema },
      { name: Order.name, schema: OrderSchema },
      { name: Supplier.name, schema: SupplierSchema },
      { name: Review.name, schema: ReviewSchema },
    ]),
  ],
  controllers: [
    DashboardController,
    ProductsController,
    OrdersController,
    SuppliersController,
    ReviewsController,
  ],
  providers: [
    DashboardService,
    ProductsService,
    OrdersService,
    SuppliersService,
    ReviewsService,
  ],
  exports: [
    DashboardService,
    ProductsService,
    OrdersService,
    SuppliersService,
    ReviewsService,
  ],
})
export class StoreModule {}
```

### Ejemplo de Controller Completo
```typescript
// src/store/controllers/products.controller.ts
import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  UseGuards,
  Req
} from '@nestjs/common';
import { ProductsService } from '../services/products.service';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { StoreGuard } from '../../auth/guards/store.guard';

@Controller('store/products')
@UseGuards(JwtAuthGuard, StoreGuard)
export class ProductsController {
  constructor(private readonly productsService: ProductsService) {}

  @Get()
  async findAll(@Req() req: any) {
    const storeId = req.user.storeId;
    return await this.productsService.findAll(storeId);
  }

  @Post()
  async create(@Body() createProductDto: CreateProductDto, @Req() req: any) {
    const storeId = req.user.storeId;
    return await this.productsService.create(createProductDto, storeId);
  }

  @Patch(':id')
  async update(
    @Param('id') id: string,
    @Body() updateProductDto: UpdateProductDto,
    @Req() req: any
  ) {
    const storeId = req.user.storeId;
    return await this.productsService.update(id, updateProductDto, storeId);
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @Req() req: any) {
    const storeId = req.user.storeId;
    return await this.productsService.remove(id, storeId);
  }
}
```

### Comandos para Crear el Proyecto
```bash
# Crear nuevo proyecto NestJS
npm i -g @nestjs/cli
nest new store-microservice

# Instalar dependencias necesarias
npm install @nestjs/mongoose mongoose
npm install @nestjs/config
npm install @nestjs/passport passport passport-jwt
npm install @nestjs/jwt
npm install class-validator class-transformer

# Instalar tipos para desarrollo
npm install -D @types/passport-jwt

# Generar módulos y servicios
nest generate module store
nest generate module auth
nest generate controller store/dashboard
nest generate controller store/products
nest generate controller store/orders
nest generate controller store/suppliers
nest generate controller store/reviews
nest generate service store/dashboard
nest generate service store/products
nest generate service store/orders
nest generate service store/suppliers
nest generate service store/reviews
```

### Archivo main.ts
```typescript
// src/main.ts
import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Habilitar validación global
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Habilitar CORS
  app.enableCors({
    origin: 'http://localhost:3000', // Frontend URL
    credentials: true,
  });

  // Prefijo global para API
  app.setGlobalPrefix('api');

  await app.listen(process.env.PORT || 3001);
  console.log(`Store Microservice running on port ${process.env.PORT || 3001}`);
}
bootstrap();
```
