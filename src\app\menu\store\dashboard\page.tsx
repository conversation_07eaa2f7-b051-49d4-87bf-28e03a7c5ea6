'use client';

import React from 'react';
import StoreLayout from '@/components/layouts/StoreLayout';
import DashboardSummary from '@/components/store/DashboardSummary';
import RecentOrders from '@/components/store/RecentOrders';
import { useDashboard, useOrders } from '@/hooks/useStore';

export default function StoreDashboardPage() {
  const { stats, loading: statsLoading, error: statsError } = useDashboard();
  const { orders, loading: ordersLoading, error: ordersError } = useOrders();

  // Obtener los pedidos más recientes (últimos 4)
  const recentOrders = orders.slice(0, 4);

  return (
    <StoreLayout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Store Dashboard</h1>
        <p className="text-gray-600">Overview of your store's performance</p>
      </div>

      <div className="mb-8">
        {statsLoading ? (
          <div className="bg-white p-6 rounded-lg border shadow-sm">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-20 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        ) : statsError ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">Error al cargar estadísticas: {statsError}</p>
          </div>
        ) : stats ? (
          <DashboardSummary stats={stats} />
        ) : null}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          {ordersLoading ? (
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            </div>
          ) : ordersError ? (
            <div className="bg-white p-6 rounded-lg border shadow-sm">
              <h2 className="text-xl font-semibold mb-4">Recent Orders</h2>
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-600">Error al cargar pedidos: {ordersError}</p>
              </div>
            </div>
          ) : (
            <RecentOrders orders={recentOrders} />
          )}
        </div>
        <div>
          <div className="bg-white p-6 rounded-lg border shadow-sm h-full">
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <div className="space-y-4">
              <a
                href="/menu/store/products"
                className="block p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center">
                  <div className="text-2xl mr-4">📦</div>
                  <div>
                    <h3 className="font-medium">Manage Products</h3>
                    <p className="text-sm text-gray-500">Add, edit or remove products from your catalog</p>
                  </div>
                </div>
              </a>
              <a
                href="/menu/store/orders"
                className="block p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center">
                  <div className="text-2xl mr-4">📋</div>
                  <div>
                    <h3 className="font-medium">View All Orders</h3>
                    <p className="text-sm text-gray-500">See all orders and their statuses</p>
                  </div>
                </div>
              </a>
              <a
                href="/menu/store/suppliers"
                className="block p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center">
                  <div className="text-2xl mr-4">🚚</div>
                  <div>
                    <h3 className="font-medium">Manage Suppliers</h3>
                    <p className="text-sm text-gray-500">Add or edit your supplier relationships</p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </StoreLayout>
  );
}
