'use client';
import { useState, useEffect } from 'react';
import { storeAPI } from '@/services/api/store';

// Hook para Dashboard - Estadísticas de la tienda
export const useDashboard = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await storeAPI.getDashboardStats();
      setStats(data);
    } catch (err: any) {
      setError(err.message || 'Error al cargar estadísticas del dashboard');
      console.error('Dashboard stats error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return { stats, loading, error, refetch: fetchStats };
};

// Hook para Pedidos - Gestión de órdenes
export const useOrders = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await storeAPI.getOrders();
      setOrders(data);
    } catch (err: any) {
      setError(err.message || 'Error al cargar pedidos');
      console.error('Orders fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (id: string, status: string) => {
    try {
      const updatedOrder = await storeAPI.updateOrderStatus(id, status);
      setOrders(prev => prev.map((o: any) => o.id === id ? updatedOrder : o));
      return updatedOrder;
    } catch (err: any) {
      setError(err.message || 'Error al actualizar estado del pedido');
      throw err;
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  return {
    orders,
    loading,
    error,
    updateOrderStatus,
    refetch: fetchOrders
  };
};

// Hook para Productos - Gestión de catálogo
export const useProducts = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await storeAPI.getProducts();
      setProducts(data);
    } catch (err: any) {
      setError(err.message || 'Error al cargar productos');
      console.error('Products fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const createProduct = async (productData: any) => {
    try {
      const newProduct = await storeAPI.createProduct(productData);
      setProducts(prev => [newProduct, ...prev]);
      return newProduct;
    } catch (err: any) {
      setError(err.message || 'Error al crear producto');
      throw err;
    }
  };

  const updateProduct = async (id: string, productData: any) => {
    try {
      const updatedProduct = await storeAPI.updateProduct(id, productData);
      setProducts(prev => prev.map((p: any) => p.id === id ? updatedProduct : p));
      return updatedProduct;
    } catch (err: any) {
      setError(err.message || 'Error al actualizar producto');
      throw err;
    }
  };

  const deleteProduct = async (id: string) => {
    try {
      await storeAPI.deleteProduct(id);
      setProducts(prev => prev.filter((p: any) => p.id !== id));
    } catch (err: any) {
      setError(err.message || 'Error al eliminar producto');
      throw err;
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  return {
    products,
    loading,
    error,
    createProduct,
    updateProduct,
    deleteProduct,
    refetch: fetchProducts
  };
};

// Hook para Proveedores - Gestión de suppliers
export const useSuppliers = () => {
  const [suppliers, setSuppliers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await storeAPI.getSuppliers();
      setSuppliers(data);
    } catch (err: any) {
      setError(err.message || 'Error al cargar proveedores');
      console.error('Suppliers fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const createSupplier = async (supplierData: any) => {
    try {
      const newSupplier = await storeAPI.createSupplier(supplierData);
      setSuppliers(prev => [newSupplier, ...prev]);
      return newSupplier;
    } catch (err: any) {
      setError(err.message || 'Error al crear proveedor');
      throw err;
    }
  };

  const updateSupplier = async (id: string, supplierData: any) => {
    try {
      const updatedSupplier = await storeAPI.updateSupplier(id, supplierData);
      setSuppliers(prev => prev.map((s: any) => s.id === id ? updatedSupplier : s));
      return updatedSupplier;
    } catch (err: any) {
      setError(err.message || 'Error al actualizar proveedor');
      throw err;
    }
  };

  const deleteSupplier = async (id: string) => {
    try {
      await storeAPI.deleteSupplier(id);
      setSuppliers(prev => prev.filter((s: any) => s.id !== id));
    } catch (err: any) {
      setError(err.message || 'Error al eliminar proveedor');
      throw err;
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  return {
    suppliers,
    loading,
    error,
    createSupplier,
    updateSupplier,
    deleteSupplier,
    refetch: fetchSuppliers
  };
};

// Hook para Reseñas - Gestión de reviews
export const useReviews = () => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await storeAPI.getReviews();
      setReviews(data);
    } catch (err: any) {
      setError(err.message || 'Error al cargar reseñas');
      console.error('Reviews fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const replyToReview = async (id: string, reply: string) => {
    try {
      const updatedReview = await storeAPI.replyToReview(id, reply);
      setReviews(prev => prev.map((r: any) => r.id === id ? updatedReview : r));
      return updatedReview;
    } catch (err: any) {
      setError(err.message || 'Error al responder reseña');
      throw err;
    }
  };

  useEffect(() => {
    fetchReviews();
  }, []);

  return {
    reviews,
    loading,
    error,
    replyToReview,
    refetch: fetchReviews
  };
};
