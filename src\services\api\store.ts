'use client';
import axios from 'axios';

class StoreAPI {
  private baseURL = 'http://localhost:3001/api/store';
  
  private getHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  // Dashboard - Estadísticas de la tienda
  async getDashboardStats() {
    try {
      const response = await axios.get(`${this.baseURL}/dashboard`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }

  // Pedidos - Gestión de órdenes
  async getOrders() {
    try {
      const response = await axios.get(`${this.baseURL}/orders`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw error;
    }
  }

  async getOrder(id: string) {
    try {
      const response = await axios.get(`${this.baseURL}/orders/${id}`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching order:', error);
      throw error;
    }
  }

  async updateOrderStatus(id: string, status: string) {
    try {
      const response = await axios.patch(`${this.baseURL}/orders/${id}/status`, 
        { status }, 
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw error;
    }
  }

  // Productos - Gestión de catálogo
  async getProducts() {
    try {
      const response = await axios.get(`${this.baseURL}/products`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  async createProduct(productData: any) {
    try {
      const response = await axios.post(`${this.baseURL}/products`, productData, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  async updateProduct(id: string, productData: any) {
    try {
      const response = await axios.patch(`${this.baseURL}/products/${id}`, productData, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  async deleteProduct(id: string) {
    try {
      const response = await axios.delete(`${this.baseURL}/products/${id}`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  }

  // Proveedores - Gestión de suppliers
  async getSuppliers() {
    try {
      const response = await axios.get(`${this.baseURL}/suppliers`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      throw error;
    }
  }

  async createSupplier(supplierData: any) {
    try {
      const response = await axios.post(`${this.baseURL}/suppliers`, supplierData, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error creating supplier:', error);
      throw error;
    }
  }

  async updateSupplier(id: string, supplierData: any) {
    try {
      const response = await axios.patch(`${this.baseURL}/suppliers/${id}`, supplierData, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error updating supplier:', error);
      throw error;
    }
  }

  async deleteSupplier(id: string) {
    try {
      const response = await axios.delete(`${this.baseURL}/suppliers/${id}`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error deleting supplier:', error);
      throw error;
    }
  }

  // Reseñas - Gestión de reviews
  async getReviews() {
    try {
      const response = await axios.get(`${this.baseURL}/reviews`, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching reviews:', error);
      throw error;
    }
  }

  async replyToReview(id: string, reply: string) {
    try {
      const response = await axios.patch(`${this.baseURL}/reviews/${id}/reply`,
        { reply },
        { headers: this.getHeaders() }
      );
      return response.data;
    } catch (error) {
      console.error('Error replying to review:', error);
      throw error;
    }
  }
}

export const storeAPI = new StoreAPI();
